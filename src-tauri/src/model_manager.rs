use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use tokio::process::Child;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelInfo {
    pub id: String,
    pub name: String,
    pub size: String,
    pub description: String,
    pub download_url: String,
    pub local_path: Option<PathBuf>,
    pub status: ModelStatus,
    pub parameters: ModelParameters,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ModelStatus {
    NotDownloaded,
    Downloading { progress: f32 },
    Downloaded,
    Loading,
    Running { port: u16 },
    Error(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelParameters {
    pub context_length: u32,
    pub temperature: f32,
    pub top_p: f32,
    pub max_tokens: u32,
}

impl Default for ModelParameters {
    fn default() -> Self {
        Self {
            context_length: 4096,
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1024,
        }
    }
}

pub struct ModelManager {
    models: Arc<Mutex<HashMap<String, ModelInfo>>>,
    running_processes: Arc<Mutex<HashMap<String, Child>>>,
    models_dir: PathBuf,
}

impl ModelManager {
    pub fn new() -> Result<Self> {
        let models_dir = dirs::home_dir()
            .ok_or_else(|| anyhow!("Could not find home directory"))?
            .join(".unthslot")
            .join("models");

        std::fs::create_dir_all(&models_dir)?;

        let mut models = HashMap::new();

        // Qwen3 系列 (最新)
        models.insert("qwen3-0.6b".to_string(), ModelInfo {
            id: "qwen3-0.6b".to_string(),
            name: "Qwen3 0.6B".to_string(),
            size: "0.6GB".to_string(),
            description: "Qwen3 0.6B - 轻量级高效模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen3-0.6B-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("qwen3-1.7b".to_string(), ModelInfo {
            id: "qwen3-1.7b".to_string(),
            name: "Qwen3 1.7B".to_string(),
            size: "1.7GB".to_string(),
            description: "Qwen3 1.7B - 平衡性能与效率".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen3-1.7B-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("qwen3-4b".to_string(), ModelInfo {
            id: "qwen3-4b".to_string(),
            name: "Qwen3 4B".to_string(),
            size: "4.0GB".to_string(),
            description: "Qwen3 4B - 中等规模高性能模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen3-4B-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("qwen3-8b".to_string(), ModelInfo {
            id: "qwen3-8b".to_string(),
            name: "Qwen3 8B".to_string(),
            size: "8.0GB".to_string(),
            description: "Qwen3 8B - 高性能通用模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen3-8B-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Phi-4 系列 (最新)
        models.insert("phi4-reasoning".to_string(), ModelInfo {
            id: "phi4-reasoning".to_string(),
            name: "Phi-4 Reasoning".to_string(),
            size: "14GB".to_string(),
            description: "Phi-4 推理模型 - 专注逻辑推理".to_string(),
            download_url: "https://huggingface.co/unsloth/Phi-4-reasoning-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("phi4-mini".to_string(), ModelInfo {
            id: "phi4-mini".to_string(),
            name: "Phi-4 Mini".to_string(),
            size: "3.8GB".to_string(),
            description: "Phi-4 Mini - 轻量级智能模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Phi-4-mini-instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Llama 4 系列 (最新)
        models.insert("llama4-scout".to_string(), ModelInfo {
            id: "llama4-scout".to_string(),
            name: "Llama 4 Scout 17B".to_string(),
            size: "17GB".to_string(),
            description: "Llama 4 Scout - 新一代大语言模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Llama-4-Scout-17B-16E-Instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Gemma 3 系列 (最新)
        models.insert("gemma3-1b".to_string(), ModelInfo {
            id: "gemma3-1b".to_string(),
            name: "Gemma 3 1B".to_string(),
            size: "1.0GB".to_string(),
            description: "Gemma 3 1B - Google 最新轻量模型".to_string(),
            download_url: "https://huggingface.co/unsloth/gemma-3-1b-it-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("gemma3-4b".to_string(), ModelInfo {
            id: "gemma3-4b".to_string(),
            name: "Gemma 3 4B".to_string(),
            size: "4.0GB".to_string(),
            description: "Gemma 3 4B - Google 中等规模模型".to_string(),
            download_url: "https://huggingface.co/unsloth/gemma-3-4b-it-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // DeepSeek-R1 系列
        models.insert("deepseek-r1-llama-8b".to_string(), ModelInfo {
            id: "deepseek-r1-llama-8b".to_string(),
            name: "DeepSeek-R1 Llama 8B".to_string(),
            size: "8.0GB".to_string(),
            description: "DeepSeek-R1 蒸馏版本 - 基于 Llama 3".to_string(),
            download_url: "https://huggingface.co/unsloth/DeepSeek-R1-Distill-Llama-8B-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Llama 3.2 系列
        models.insert("llama3.2-1b".to_string(), ModelInfo {
            id: "llama3.2-1b".to_string(),
            name: "Llama 3.2 1B".to_string(),
            size: "1.0GB".to_string(),
            description: "Llama 3.2 1B - Meta 轻量级模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Llama-3.2-1B-Instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("llama3.2-3b".to_string(), ModelInfo {
            id: "llama3.2-3b".to_string(),
            name: "Llama 3.2 3B".to_string(),
            size: "3.0GB".to_string(),
            description: "Llama 3.2 3B - Meta 中等规模模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Llama-3.2-3B-Instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Llama 3.1 系列
        models.insert("llama3.1-8b".to_string(), ModelInfo {
            id: "llama3.1-8b".to_string(),
            name: "Llama 3.1 8B".to_string(),
            size: "8.0GB".to_string(),
            description: "Llama 3.1 8B - Meta 高性能模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Llama-3.1-8B-Instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Mistral 系列
        models.insert("mistral-nemo-12b".to_string(), ModelInfo {
            id: "mistral-nemo-12b".to_string(),
            name: "Mistral NeMo 12B".to_string(),
            size: "12GB".to_string(),
            description: "Mistral NeMo 12B - 高效多语言模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Mistral-Nemo-Instruct-2407-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // Qwen 2.5 系列
        models.insert("qwen2.5-0.5b".to_string(), ModelInfo {
            id: "qwen2.5-0.5b".to_string(),
            name: "Qwen 2.5 0.5B".to_string(),
            size: "0.5GB".to_string(),
            description: "Qwen 2.5 0.5B - 超轻量级模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen2.5-0.5B-Instruct-bnb-4bit".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("qwen2.5-1.5b".to_string(), ModelInfo {
            id: "qwen2.5-1.5b".to_string(),
            name: "Qwen 2.5 1.5B".to_string(),
            size: "1.5GB".to_string(),
            description: "Qwen 2.5 1.5B - 轻量高效模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen2.5-1.5B-Instruct-bnb-4bit".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("qwen2.5-7b".to_string(), ModelInfo {
            id: "qwen2.5-7b".to_string(),
            name: "Qwen 2.5 7B".to_string(),
            size: "7.0GB".to_string(),
            description: "Qwen 2.5 7B - 高性能通用模型".to_string(),
            download_url: "https://huggingface.co/unsloth/Qwen2.5-7B-Instruct-bnb-4bit".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        // SmolLM2 系列 (轻量级)
        models.insert("smollm2-135m".to_string(), ModelInfo {
            id: "smollm2-135m".to_string(),
            name: "SmolLM2 135M".to_string(),
            size: "135MB".to_string(),
            description: "SmolLM2 135M - 超小型模型，适合边缘设备".to_string(),
            download_url: "https://huggingface.co/unsloth/SmolLM2-135M-Instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        models.insert("smollm2-1.7b".to_string(), ModelInfo {
            id: "smollm2-1.7b".to_string(),
            name: "SmolLM2 1.7B".to_string(),
            size: "1.7GB".to_string(),
            description: "SmolLM2 1.7B - 小型高效模型".to_string(),
            download_url: "https://huggingface.co/unsloth/SmolLM2-1.7B-Instruct-GGUF".to_string(),
            local_path: None,
            status: ModelStatus::NotDownloaded,
            parameters: ModelParameters::default(),
        });

        Ok(Self {
            models: Arc::new(Mutex::new(models)),
            running_processes: Arc::new(Mutex::new(HashMap::new())),
            models_dir,
        })
    }

    pub fn get_models(&self) -> Result<Vec<ModelInfo>> {
        let models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
        Ok(models.values().cloned().collect())
    }

    pub fn get_model(&self, id: &str) -> Result<Option<ModelInfo>> {
        let models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
        Ok(models.get(id).cloned())
    }

    pub async fn download_model(&self, id: &str) -> Result<()> {
        // 更新状态为下载中
        {
            let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            if let Some(model) = models.get_mut(id) {
                model.status = ModelStatus::Downloading { progress: 0.0 };
            } else {
                return Err(anyhow!("Model not found: {}", id));
            }
        }

        // 这里应该实现实际的下载逻辑
        // 为了演示，我们模拟下载过程
        for i in 1..=10 {
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            let progress = (i as f32) * 10.0;

            let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            if let Some(model) = models.get_mut(id) {
                model.status = ModelStatus::Downloading { progress };
            }
        }

        // 下载完成
        {
            let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            if let Some(model) = models.get_mut(id) {
                model.local_path = Some(self.models_dir.join(&model.name));
                model.status = ModelStatus::Downloaded;
            }
        }

        Ok(())
    }

    pub async fn start_model(&self, id: &str) -> Result<u16> {
        // 检查模型是否已下载
        let model = {
            let models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            models.get(id).cloned().ok_or_else(|| anyhow!("Model not found: {}", id))?
        };

        if !matches!(model.status, ModelStatus::Downloaded) {
            return Err(anyhow!("Model not downloaded"));
        }

        // 找一个可用端口
        let port = 8080; // 简化实现，实际应该动态分配

        // 更新状态为加载中
        {
            let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            if let Some(model) = models.get_mut(id) {
                model.status = ModelStatus::Loading;
            }
        }

        // 启动模型服务（这里应该启动实际的模型推理服务）
        // 为了演示，我们创建一个简单的 HTTP 服务器
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        // 更新状态为运行中
        {
            let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            if let Some(model) = models.get_mut(id) {
                model.status = ModelStatus::Running { port };
            }
        }

        Ok(port)
    }

    pub async fn stop_model(&self, id: &str) -> Result<()> {
        // 停止模型进程
        let mut process_to_kill = {
            let mut processes = self.running_processes.lock().map_err(|_| anyhow!("Failed to lock processes"))?;
            processes.remove(id)
        };

        if let Some(mut process) = process_to_kill {
            let _ = process.kill().await;
        }

        // 更新状态
        {
            let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
            if let Some(model) = models.get_mut(id) {
                model.status = ModelStatus::Downloaded;
            }
        }

        Ok(())
    }

    pub fn update_model_parameters(&self, id: &str, parameters: ModelParameters) -> Result<()> {
        let mut models = self.models.lock().map_err(|_| anyhow!("Failed to lock models"))?;
        if let Some(model) = models.get_mut(id) {
            model.parameters = parameters;
            Ok(())
        } else {
            Err(anyhow!("Model not found: {}", id))
        }
    }
}
