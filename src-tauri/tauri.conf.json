{"$schema": "https://schema.tauri.app/config/2", "productName": "unthslot", "version": "0.1.0", "identifier": "com.unthslot.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "UnthSlot - 本地模型部署客户端", "width": 800, "height": 800, "minWidth": 600, "minHeight": 500, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}