{"name": "unthslot", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/node": "^22.15.21", "autoprefixer": "^10.4.21", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^4.1.7"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}