import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>D<PERSON>, <PERSON><PERSON>, Play } from 'lucide-react';
import { ApiService, ModelInfo, ModelStatus } from '../services/api';

interface ModelSelectorProps {
  selectedModelId: string;
  onModelChange: (modelId: string) => void;
  className?: string;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModelId,
  onModelChange,
  className = '',
}) => {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      setLoading(true);
      const modelList = await ApiService.getModels();
      setModels(modelList);
    } catch (error) {
      console.error('Failed to load models:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAvailableModels = () => {
    return models.filter(model => 
      model.status === 'Downloaded' || 
      (typeof model.status === 'object' && 'Running' in model.status)
    );
  };

  const selectedModel = models.find(m => m.id === selectedModelId);
  const availableModels = getAvailableModels();

  const isModelRunning = (status: ModelStatus): boolean => {
    return typeof status === 'object' && 'Running' in status;
  };

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsOpen(false);
  };

  const handleStartModel = async (modelId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await ApiService.startModel(modelId);
      await loadModels();
    } catch (error) {
      console.error('Failed to start model:', error);
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-gray-700 rounded-lg ${className}`}>
        <div className="w-4 h-4 border-2 border-purple border-t-transparent rounded-full animate-spin"></div>
        <span className="text-sm text-gray-400">加载模型...</span>
      </div>
    );
  }

  if (availableModels.length === 0) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-gray-700 rounded-lg ${className}`}>
        <Bot size={16} className="text-gray-400" />
        <span className="text-sm text-gray-400">没有可用的模型</span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between gap-2 p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
      >
        <div className="flex items-center gap-2">
          <Bot size={16} className="text-purple" />
          <div className="text-left">
            <div className="text-sm font-medium text-white">
              {selectedModel ? selectedModel.name : '选择模型'}
            </div>
            {selectedModel && (
              <div className="text-xs text-gray-400">
                {isModelRunning(selectedModel.status) ? (
                  <span className="text-green">● 运行中</span>
                ) : (
                  <span className="text-yellow">● 已下载</span>
                )}
              </div>
            )}
          </div>
        </div>
        <ChevronDown 
          size={16} 
          className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
          {availableModels.map((model) => (
            <div
              key={model.id}
              className="flex items-center justify-between p-3 hover:bg-gray-600 cursor-pointer transition-colors"
              onClick={() => handleModelSelect(model.id)}
            >
              <div className="flex items-center gap-2">
                <Bot size={14} className="text-purple" />
                <div>
                  <div className="text-sm font-medium text-white">{model.name}</div>
                  <div className="text-xs text-gray-400">{model.size}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {isModelRunning(model.status) ? (
                  <span className="text-xs text-green">● 运行中</span>
                ) : (
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-yellow">● 已下载</span>
                    <button
                      onClick={(e) => handleStartModel(model.id, e)}
                      className="p-1 hover:bg-gray-500 rounded transition-colors"
                      title="启动模型"
                    >
                      <Play size={12} className="text-green" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {availableModels.length === 0 && (
            <div className="p-3 text-center text-gray-400 text-sm">
              没有可用的模型，请先下载并启动模型
            </div>
          )}
        </div>
      )}
    </div>
  );
};
