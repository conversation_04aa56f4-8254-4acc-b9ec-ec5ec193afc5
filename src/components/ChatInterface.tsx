import React, { useState, useEffect, useRef } from 'react';
import { Send, Bot, User, Co<PERSON>, RotateCcw, Settings } from 'lucide-react';
import { ApiService, ChatSession, ChatMessage, ChatRequest } from '../services/api';
import { ModelSelector } from './ModelSelector';

interface ChatInterfaceProps {
  session: ChatSession | null;
  onSessionUpdate: (session: ChatSession) => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({ session, onSessionUpdate }) => {
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedModelId, setSelectedModelId] = useState('qwen3-1.7b');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (session) {
      setMessages(session.messages);
      setSelectedModelId(session.model_id);
    } else {
      setMessages([]);
    }
  }, [session]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !session || isLoading) return;

    const userMessage = message.trim();
    setMessage('');
    setIsLoading(true);

    try {
      const request: ChatRequest = {
        message: userMessage,
        session_id: session.id,
        model_id: selectedModelId,
        stream: false,
      };

      const response = await ApiService.sendChatMessage(request);

      // 更新本地消息列表
      const updatedSession = await ApiService.getChatSession(session.id);
      if (updatedSession) {
        setMessages(updatedSession.messages);
        onSessionUpdate(updatedSession);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      // 可以在这里添加错误提示
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 可以添加复制成功的提示
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleModelChange = (modelId: string) => {
    setSelectedModelId(modelId);
  };

  const formatMessageTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!session) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Bot size={64} className="text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-600 mb-2">欢迎使用 UnthSlot</h2>
          <p className="text-gray-500">选择一个模型开始对话，或创建新的聊天会话</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* 聊天头部 */}
      <div className="border-b border-gray-700 p-4 bg-gray-800">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h2 className="text-lg font-semibold text-white">{session.title}</h2>
            <p className="text-sm text-gray-400">当前会话</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => {/* 实现重新生成功能 */}}
              className="p-2 text-gray-400 hover:text-cyan hover:bg-gray-700 rounded-lg transition-colors"
              title="重新生成"
            >
              <RotateCcw size={20} />
            </button>
          </div>
        </div>

        {/* 模型选择器 */}
        <ModelSelector
          selectedModelId={selectedModelId}
          onModelChange={handleModelChange}
          className="w-full"
        />
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin bg-gray-900">
        {messages.length === 0 ? (
          <div className="text-center text-gray-400 mt-8">
            <Bot size={48} className="mx-auto mb-4 text-purple" />
            <p>开始对话吧！我是您的 AI 助手。</p>
          </div>
        ) : (
          messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex gap-3 ${msg.role === 'User' ? 'justify-end' : 'justify-start'}`}
            >
              {msg.role === 'Assistant' && (
                <div className="w-8 h-8 bg-purple rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot size={16} className="text-white" />
                </div>
              )}

              <div className={`max-w-[70%] ${msg.role === 'User' ? 'order-2' : ''}`}>
                <div
                  className={`p-3 rounded-lg ${
                    msg.role === 'User'
                      ? 'bg-purple text-white'
                      : 'bg-gray-700 text-white'
                  }`}
                >
                  <div className="whitespace-pre-wrap break-words">{msg.content}</div>
                </div>

                <div className={`flex items-center gap-2 mt-1 text-xs text-gray-400 ${
                  msg.role === 'User' ? 'justify-end' : 'justify-start'
                }`}>
                  <span>{formatMessageTime(msg.timestamp)}</span>
                  {msg.role === 'Assistant' && (
                    <button
                      onClick={() => copyToClipboard(msg.content)}
                      className="p-1 hover:bg-gray-600 rounded transition-colors"
                      title="复制"
                    >
                      <Copy size={12} />
                    </button>
                  )}
                </div>
              </div>

              {msg.role === 'User' && (
                <div className="w-8 h-8 bg-cyan rounded-full flex items-center justify-center flex-shrink-0 order-3">
                  <User size={16} className="text-gray-900" />
                </div>
              )}
            </div>
          ))
        )}

        {/* 加载指示器 */}
        {isLoading && (
          <div className="flex gap-3 justify-start">
            <div className="w-8 h-8 bg-purple rounded-full flex items-center justify-center flex-shrink-0">
              <Bot size={16} className="text-white" />
            </div>
            <div className="bg-gray-700 p-3 rounded-lg">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="border-t border-gray-700 p-4 bg-gray-800">
        <div className="flex gap-3">
          <div className="flex-1 relative">
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="输入您的消息... (Shift+Enter 换行)"
              className="w-full border border-gray-600 bg-gray-700 text-white rounded-lg px-4 py-3 pr-12 resize-none focus:outline-none focus:ring-2 focus:ring-purple focus:border-transparent placeholder-gray-400"
              rows={1}
              style={{
                minHeight: '44px',
                maxHeight: '120px',
                height: 'auto',
              }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = Math.min(target.scrollHeight, 120) + 'px';
              }}
              disabled={isLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={!message.trim() || isLoading}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-purple hover:text-cyan disabled:text-gray-500 disabled:cursor-not-allowed transition-colors"
            >
              <Send size={20} />
            </button>
          </div>
        </div>

        <div className="flex justify-between items-center mt-2 text-xs text-gray-400">
          <span>按 Enter 发送，Shift+Enter 换行</span>
          <span>{message.length} 字符</span>
        </div>
      </div>
    </div>
  );
};
