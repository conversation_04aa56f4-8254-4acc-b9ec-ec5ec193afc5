/* Dracula Theme 基础样式 */
:root {
  /* Dracula Theme Colors */
  --background: #282a36;
  --current-line: #44475a;
  --foreground: #f8f8f2;
  --comment: #6272a4;
  --cyan: #8be9fd;
  --green: #50fa7b;
  --orange: #ffb86c;
  --pink: #ff79c6;
  --purple: #bd93f9;
  --red: #ff5555;
  --yellow: #f1fa8c;
  --selection: #44475a;
  --darker-bg: #21222c;
  --lighter-bg: #343746;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background-color: var(--background);
  color: var(--foreground);
}

/* 工具类 */
.h-screen { height: 100vh; }
.h-full { height: 100%; }
.w-full { width: 100%; }
.w-16 { width: 4rem; }
.w-64 { width: 16rem; }
.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-end: flex-end; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.ml-6 { margin-left: 1.5rem; }
.mr-5 { margin-right: 1.25rem; }
/* Dracula Theme 背景色 */
.bg-white { background-color: var(--background); }
.bg-gray-50 { background-color: var(--lighter-bg); }
.bg-gray-100 { background-color: var(--current-line); }
.bg-gray-200 { background-color: var(--selection); }
.bg-gray-600 { background-color: var(--current-line); }
.bg-gray-700 { background-color: var(--darker-bg); }
.bg-gray-800 { background-color: var(--darker-bg); }
.bg-gray-900 { background-color: var(--darker-bg); }
.bg-blue-600 { background-color: var(--purple); }
.bg-blue-700 { background-color: var(--purple); }
.bg-green-600 { background-color: var(--green); }
.bg-green-700 { background-color: var(--green); }
.bg-red-600 { background-color: var(--red); }
.bg-red-700 { background-color: var(--red); }
.bg-yellow-100 { background-color: rgba(241, 250, 140, 0.1); }
.bg-blue-100 { background-color: rgba(189, 147, 249, 0.1); }
/* Dracula Theme 文字颜色 */
.text-white { color: var(--foreground); }
.text-gray-300 { color: var(--comment); }
.text-gray-400 { color: var(--comment); }
.text-gray-500 { color: var(--comment); }
.text-gray-600 { color: var(--foreground); }
.text-gray-700 { color: var(--foreground); }
.text-gray-800 { color: var(--foreground); }
.text-gray-900 { color: var(--foreground); }
.text-blue-400 { color: var(--cyan); }
.text-blue-600 { color: var(--purple); }
.text-blue-800 { color: var(--purple); }
.text-green-500 { color: var(--green); }
.text-green-600 { color: var(--green); }
.text-red-400 { color: var(--red); }
.text-red-500 { color: var(--red); }
.text-yellow-600 { color: var(--yellow); }
.text-yellow-800 { color: var(--orange); }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }
.border { border-width: 1px; }
/* Dracula Theme 边框颜色 */
.border-gray-200 { border-color: var(--current-line); }
.border-gray-300 { border-color: var(--current-line); }
.border-gray-700 { border-color: var(--current-line); }
.border-b { border-bottom-width: 1px; }
.border-t { border-top-width: 1px; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.cursor-pointer { cursor: pointer; }
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }
/* Dracula Theme 悬停效果 */
.hover\:bg-gray-100:hover { background-color: var(--selection); }
.hover\:bg-gray-600:hover { background-color: var(--selection); }
.hover\:bg-gray-700:hover { background-color: var(--current-line); }
.hover\:bg-gray-800:hover { background-color: var(--current-line); }
.hover\:bg-blue-700:hover { background-color: var(--purple); }
.hover\:bg-green-700:hover { background-color: var(--green); }
.hover\:bg-red-700:hover { background-color: var(--red); }
.hover\:text-gray-800:hover { color: var(--cyan); }
.hover\:text-gray-900:hover { color: var(--cyan); }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\:ring-blue-500:focus { --tw-ring-color: var(--purple); }
.focus\:border-transparent:focus { border-color: transparent; }
.disabled\:text-gray-400:disabled { color: #9ca3af; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.group:hover .group-hover\:opacity-100 { opacity: 1; }
.opacity-0 { opacity: 0; }
.opacity-100 { opacity: 1; }
.max-w-full { max-width: 100%; }
.max-w-2xl { max-width: 42rem; }
.max-h-96 { max-height: 24rem; }
.max-h-120 { max-height: 30rem; }
.space-y-1 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.25rem; }
.space-y-2 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.5rem; }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
.space-y-8 > :not([hidden]) ~ :not([hidden]) { margin-top: 2rem; }
.grid { display: grid; }
.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.break-words { overflow-wrap: break-word; }
.text-center { text-align: center; }
.resize-none { resize: none; }
.flex-shrink-0 { flex-shrink: 0; }
.order-2 { order: 2; }
.order-3 { order: 3; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border-width: 0; }
.fixed { position: fixed; }
.inset-0 { top: 0px; right: 0px; bottom: 0px; left: 0px; }
.z-50 { z-index: 50; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.w-96 { width: 24rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.w-11 { width: 2.75rem; }
.h-5 { height: 1.25rem; }
.w-5 { width: 1.25rem; }
.animate-spin { animation: spin 1s linear infinite; }
.bg-black { background-color: black; }
.bg-opacity-50 { background-color: rgb(0 0 0 / 0.5); }
.min-h-44 { min-height: 11rem; }
.step-0\.1 { step: 0.1; }
.min-0 { min-width: 0; }
.max-1 { max-width: 1; }
.max-2 { max-width: 2; }
.block { display: block; }
.relative { position: relative; }
.absolute { position: absolute; }
.top-1\/2 { top: 50%; }
.right-2 { right: 0.5rem; }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.-translate-y-1\/2 { --tw-translate-y: -50%; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 自定义样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f7fafc;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 消息动画 */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* 加载动画 */
.typing-indicator {
  display: inline-flex;
  align-items: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #9ca3af;
  display: inline-block;
  margin: 0 1px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
